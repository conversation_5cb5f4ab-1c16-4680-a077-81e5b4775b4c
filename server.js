const net = require('net');
const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const path = require('path');
const fs = require('fs');

// Express app setup
const app = express();
const defaultPort = 3000;

// Always use the public directory in the current working directory
let publicPath = path.join(process.cwd(), 'public');

// Check if the public directory exists at the determined path
if (fs.existsSync(publicPath)) {
  console.log(`Serving static files from: ${publicPath}`);
} else {
  console.error(`Public directory not found at path: ${publicPath}`);
  // Try alternative paths
  const alternatives = [
    path.join(__dirname, 'public'),
    path.join(__dirname, '..', 'public'),
    path.join(process.cwd(), 'public'),
    path.join(process.cwd(), 'resources', 'app', 'public')
  ];

  for (const alt of alternatives) {
    if (fs.existsSync(alt)) {
      console.log(`Using alternative public path: ${alt}`);
      publicPath = alt;
      break;
    }
  }
}

// Serve static files from the public directory
app.use(express.static(publicPath));

// Ensure index.html is served for all routes
app.get('*', (req, res) => {
  res.sendFile(path.join(publicPath, 'index.html'));
});

// Create HTTP server
const server = http.createServer(app);

// WebSocket server
const wss = new WebSocket.Server({ server });

// Function to load router configuration from file
function loadRouterConfig() {
  const newConfigPath = path.join(process.cwd(), 'Configurations/RouterConfig/router-config.json');
  const oldConfigPath = path.join(process.cwd(), 'router-config.json');

  // Try new path first
  try {
    if (fs.existsSync(newConfigPath)) {
      const configData = fs.readFileSync(newConfigPath, 'utf8');
      const savedConfig = JSON.parse(configData);
      console.log('Loaded router configuration from new location:', savedConfig);
      return savedConfig;
    }
  } catch (error) {
    console.error('Error loading router configuration from new location:', error.message);
  }

  // Try old path for backward compatibility
  try {
    if (fs.existsSync(oldConfigPath)) {
      const configData = fs.readFileSync(oldConfigPath, 'utf8');
      const savedConfig = JSON.parse(configData);
      console.log('Loaded router configuration from old location (migrating):', savedConfig);

      // Migrate to new location
      try {
        const newDirPath = path.join(process.cwd(), 'Configurations/RouterConfig');
        if (!fs.existsSync(newDirPath)) {
          fs.mkdirSync(newDirPath, { recursive: true });
        }
        fs.writeFileSync(newConfigPath, JSON.stringify(savedConfig, null, 2), 'utf8');
        console.log('Migrated router configuration to new location');

        // Optionally remove old file
        fs.unlinkSync(oldConfigPath);
        console.log('Removed old router configuration file');
      } catch (migrationError) {
        console.error('Error migrating router configuration:', migrationError.message);
      }

      return savedConfig;
    }
  } catch (error) {
    console.error('Error loading router configuration from old location:', error.message);
  }

  // Return default configuration if loading fails
  return {
    host: '*************',
    port: 4000,
    protocol: 'quartz',
    inputs: 96,    // Default number of inputs
    outputs: 384   // Default number of outputs
  };
}

// Function to save router configuration to file
function saveRouterConfig() {
  const configPath = path.join(process.cwd(), 'Configurations/RouterConfig/router-config.json');
  try {
    fs.writeFileSync(configPath, JSON.stringify(routerConfig, null, 2), 'utf8');
    console.log('Saved router configuration to file');
  } catch (error) {
    console.error('Error saving router configuration:', error.message);
  }
}

// Router configuration - load from file or use defaults
const routerConfig = loadRouterConfig();

// Store names from router
const routerNames = {
  sources: {},
  destinations: {}
};

let routerClient = null;
let routerConnected = false;
let routerData = '';
let activeClients = new Set();
let manualDisconnect = false; // Track if disconnection was manual
let connectionAttempts = 0; // Track number of connection attempts
let maxConnectionAttempts = 3; // Maximum number of connection attempts

// Connect to router
function connectToRouter() {
  if (routerClient) {
    routerClient.destroy();
  }

  // Increment connection attempt counter
  connectionAttempts++;
  console.log(`Connection attempt ${connectionAttempts} of ${maxConnectionAttempts} to router at ${routerConfig.host}:${routerConfig.port}`);

  routerClient = new net.Socket();

  // Set a connection timeout of 30 seconds (increased for name loading)
  routerClient.setTimeout(30000);

  routerClient.connect(routerConfig.port, routerConfig.host, () => {
    console.log('Connected to router');
    routerConnected = true;
    manualDisconnect = false; // Reset manual disconnect flag on successful connection
    connectionAttempts = 0; // Reset connection attempts counter on successful connection

    broadcastToClients({
      type: 'connection',
      status: 'connected'
    });

    // Once connected, fetch the current crosspoint and lock status
    queryCurrentStateWithoutNames(); // Query routing state but skip name loading
  });

  routerClient.on('data', (data) => {
    const dataStr = data.toString();
    routerData += dataStr;

    // Process complete messages (they end with carriage return)
    while (routerData.includes('\r')) {
      const endIndex = routerData.indexOf('\r');
      const message = routerData.substring(0, endIndex);
      routerData = routerData.substring(endIndex + 1);

      console.log('Received from router:', message);

      // Parse router responses for names
      processRouterResponse(message);

      // Broadcast the response to all clients
      broadcastToClients({
        type: 'routerResponse',
        message: message
      });
    }
  });

  routerClient.on('close', () => {
    console.log('Connection to router closed');
    routerConnected = false;
    broadcastToClients({
      type: 'connection',
      status: 'disconnected'
    });

    // Try to reconnect if we haven't reached the maximum number of attempts
    // and it wasn't a manual disconnect
    if (!manualDisconnect && connectionAttempts < maxConnectionAttempts) {
      console.log(`Attempting reconnection in 3 seconds (attempt ${connectionAttempts} of ${maxConnectionAttempts})...`);
      setTimeout(connectToRouter, 3000);
    } else if (!manualDisconnect && connectionAttempts >= maxConnectionAttempts) {
      // We've reached the maximum number of attempts
      console.log('Maximum connection attempts reached. Connection failed.');
      connectionAttempts = 0; // Reset for next manual connection attempt

      // Send connection failed message to clients
      broadcastToClients({
        type: 'connection',
        status: 'failed',
        message: 'Connection failed after multiple attempts'
      });
    }
  });

  routerClient.on('timeout', () => {
    console.log('Connection timeout');
    routerClient.destroy();
  });

  routerClient.on('error', (err) => {
    console.error('Router connection error:', err.message);
    broadcastToClients({
      type: 'connection',
      status: 'error',
      message: err.message
    });
  });
}

// Manually disconnect from router
function disconnectFromRouter() {
  if (routerClient) {
    console.log('Manually disconnecting from router');
    manualDisconnect = true; // Set flag to prevent auto-reconnect
    routerClient.destroy();
    routerClient = null;
    routerConnected = false;
    broadcastToClients({
      type: 'connection',
      status: 'disconnected'
    });
  }
}

// Process router responses
function processRouterResponse(message) {
  // Check if we're using the RCP3 protocol
  if (routerConfig.protocol === 'rcp3') {
    // Process RCP3 responses
    processRCP3Response(message);
    return;
  }

  // Process Quartz protocol responses

  // Source name response format: .RAS{source},{name} or .RAS{name}
  if (message.startsWith('.RAS')) {
    const matches = message.match(/\.RAS(\d+)(?:,\s*(.+))?/) || message.match(/\.RAS(.+)/);
    if (matches) {
      // If format is .RAS{source},{name}
      if (matches.length === 3 && matches[2]) {
        const sourceNum = parseInt(matches[1]);
        const sourceName = matches[2].trim();
        routerNames.sources[sourceNum] = sourceName;
      }
      // If format is .RAS{name}
      else if (matches.length === 2) {
        const sourceName = matches[1].trim();
        // Since we can't determine the source number, store with last requested source
        if (lastRequestedSource) {
          routerNames.sources[lastRequestedSource] = sourceName;
          lastRequestedSource = null;
        }
      }

      // Broadcast the updated names
      broadcastToClients({
        type: 'sourceNames',
        names: routerNames.sources
      });
    }
  }

  // Destination name response format: .RAD{dest},{name} or .RAD{name}
  if (message.startsWith('.RAD')) {
    const matches = message.match(/\.RAD(\d+)(?:,\s*(.+))?/) || message.match(/\.RAD(.+)/);
    if (matches) {
      // If format is .RAD{dest},{name}
      if (matches.length === 3 && matches[2]) {
        const destNum = parseInt(matches[1]);
        const destName = matches[2].trim();
        routerNames.destinations[destNum] = destName;
      }
      // If format is .RAD{name}
      else if (matches.length === 2) {
        const destName = matches[1].trim();
        // Since we can't determine the destination number, store with last requested destination
        if (lastRequestedDest) {
          routerNames.destinations[lastRequestedDest] = destName;
          lastRequestedDest = null;
        }
      }

      // Broadcast the updated names
      broadcastToClients({
        type: 'destinationNames',
        names: routerNames.destinations
      });
    }
  }
}

// Process RCP3 protocol responses
function processRCP3Response(message) {
  // This is a simplified implementation of RCP3 response processing
  // In a real implementation, this would parse binary packets with headers and checksums

  // For our simplified implementation, we'll use string-based responses

  // Process Take response
  if (message.includes('RCP3_TAKE_RESPONSE')) {
    // Example format: RCP3_TAKE_RESPONSE:1,2,1
    // This means destination 1 is now connected to source 2 on level 1
    const parts = message.split(':')[1].split(',');
    if (parts.length >= 3) {
      const destNum = parseInt(parts[0]);
      const sourceNum = parseInt(parts[1]);

      // Update our internal state
      // In a real implementation, this would be more sophisticated

      // Broadcast the updated status
      broadcastToClients({
        type: 'routerResponse',
        message: `.A${destNum},${sourceNum}`  // Format compatible with frontend
      });
    }
  }

  // Process Get Matrix response
  else if (message.includes('RCP3_MATRIX_RESPONSE')) {
    // Example format: RCP3_MATRIX_RESPONSE:1,2
    // This means destination 1 is connected to source 2
    const parts = message.split(':')[1].split(',');
    if (parts.length >= 2) {
      const destNum = parseInt(parts[0]);
      const sourceNum = parseInt(parts[1]);

      // Broadcast the updated status
      broadcastToClients({
        type: 'routerResponse',
        message: `.A1${destNum},${sourceNum}`  // Format compatible with frontend
      });
    }
  }

  // Process Lock status response
  else if (message.includes('RCP3_LOCK_STATUS')) {
    // Example format: RCP3_LOCK_STATUS:1,1
    // This means destination 1 is locked (1) or unlocked (0)
    const parts = message.split(':')[1].split(',');
    if (parts.length >= 2) {
      const destNum = parseInt(parts[0]);
      const lockStatus = parseInt(parts[1]);

      // Broadcast the updated status
      broadcastToClients({
        type: 'routerResponse',
        message: `.BA${destNum},${lockStatus}`  // Format compatible with frontend
      });
    }
  }

  // Process Source Name response
  else if (message.includes('RCP3_SOURCE_NAME')) {
    // Example format: RCP3_SOURCE_NAME:1,Source 1
    const parts = message.split(':')[1].split(',');
    if (parts.length >= 2) {
      const sourceNum = parseInt(parts[0]);
      const sourceName = parts.slice(1).join(',');

      // Update our internal state
      routerNames.sources[sourceNum] = sourceName;

      // Broadcast the updated names
      broadcastToClients({
        type: 'sourceNames',
        names: routerNames.sources
      });
    }
  }

  // Process Destination Name response
  else if (message.includes('RCP3_DEST_NAME')) {
    // Example format: RCP3_DEST_NAME:1,Destination 1
    const parts = message.split(':')[1].split(',');
    if (parts.length >= 2) {
      const destNum = parseInt(parts[0]);
      const destName = parts.slice(1).join(',');

      // Update our internal state
      routerNames.destinations[destNum] = destName;

      // Broadcast the updated names
      broadcastToClients({
        type: 'destinationNames',
        names: routerNames.destinations
      });
    }
  }
}

// Track last requested source/destination for name responses
let lastRequestedSource = null;
let lastRequestedDest = null;

// WebSocket connection handler
wss.on('connection', (ws) => {
  console.log('Client connected');
  activeClients.add(ws);

  // Send the current connection status to the new client
  ws.send(JSON.stringify({
    type: 'connection',
    status: routerConnected ? 'connected' : 'disconnected'
  }));

  // Send the current protocol information
  ws.send(JSON.stringify({
    type: 'protocolUpdate',
    protocol: routerConfig.protocol
  }));

  // If we have any source/destination names, send them
  if (Object.keys(routerNames.sources).length > 0) {
    ws.send(JSON.stringify({
      type: 'sourceNames',
      names: routerNames.sources
    }));
  }

  if (Object.keys(routerNames.destinations).length > 0) {
    ws.send(JSON.stringify({
      type: 'destinationNames',
      names: routerNames.destinations
    }));
  }

  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      console.log('Received from client:', data);

      if (data.command === 'connectRouter') {
        console.log('Manual connection request received');

        // Update router configuration if provided
        if (data.config && data.config.host && data.config.port) {
          routerConfig.host = data.config.host;
          routerConfig.port = data.config.port;

          // Update router size if provided
          if (data.config.inputs) {
            routerConfig.inputs = data.config.inputs;
          }
          if (data.config.outputs) {
            routerConfig.outputs = data.config.outputs;
          }

          console.log(`Using router configuration: ${routerConfig.host}:${routerConfig.port} (${routerConfig.inputs} inputs, ${routerConfig.outputs} outputs)`);
        }

        // Only connect if not already connected
        if (!routerConnected) {
          connectToRouter();
        }
        return;
      } else if (data.command === 'disconnectRouter') {
        console.log('Manual disconnection request received');
        disconnectFromRouter();
        return;
      } else if (data.command === 'updateRouterConfig') {
        console.log('Router configuration update received:', data.config);
        if (data.config && data.config.host && data.config.port) {
          // Update router configuration
          routerConfig.host = data.config.host;
          routerConfig.port = data.config.port;

          // Update protocol if provided
          if (data.config.protocol) {
            routerConfig.protocol = data.config.protocol;

            // Broadcast the updated protocol to all clients
            broadcastToClients({
              type: 'protocolUpdate',
              protocol: routerConfig.protocol
            });
          }

          // Update router size if provided
          if (data.config.inputs) {
            routerConfig.inputs = data.config.inputs;
          }
          if (data.config.outputs) {
            routerConfig.outputs = data.config.outputs;
          }

          // Log the complete configuration
          console.log(`Updated router configuration: ${routerConfig.host}:${routerConfig.port}, Protocol: ${routerConfig.protocol}, Inputs: ${routerConfig.inputs}, Outputs: ${routerConfig.outputs}`);

          // Save the updated configuration to file
          saveRouterConfig();

          // If currently connected, disconnect and reconnect with new settings
          if (routerConnected) {
            disconnectFromRouter();
            setTimeout(() => {
              connectToRouter();
            }, 1000);
          }
        }
        return;
      // updateProtocolConfig command is now handled in updateRouterConfig
      }

      if (data.command && routerConnected) {
        // Handle name query commands for Quartz protocol
        if (routerConfig.protocol === 'quartz') {
          if (data.command.startsWith('.RS')) {
            // Track the requested source for response handling
            const sourceMatch = data.command.match(/\.RS(\d+)/);
            if (sourceMatch) {
              lastRequestedSource = parseInt(sourceMatch[1]);
            }
          } else if (data.command.startsWith('.RD')) {
            // Track the requested destination for response handling
            const destMatch = data.command.match(/\.RD(\d+)/);
            if (destMatch) {
              lastRequestedDest = parseInt(destMatch[1]);
            }
          } else if (data.command.startsWith('.NS')) {
            // Handle set source name command
            const match = data.command.match(/\.NS(\d+),(.+)/);
            if (match) {
              const sourceNum = parseInt(match[1]);
              const sourceName = match[2];
              console.log(`Setting source name: ${sourceNum} to "${sourceName}"`);

              // Store the name in our local cache
              routerNames.sources[sourceNum] = sourceName;
            }
          } else if (data.command.startsWith('.ND')) {
            // Handle set destination name command
            const match = data.command.match(/\.ND(\d+),(.+)/);
            if (match) {
              const destNum = parseInt(match[1]);
              const destName = match[2];
              console.log(`Setting destination name: ${destNum} to "${destName}"`);

              // Store the name in our local cache
              routerNames.destinations[destNum] = destName;
            }
          }
        }

        // Send command to router using the appropriate protocol
        sendRouterCommand(data.command);

        // Broadcast to all clients
        broadcastToClients({
          type: 'routerCommand',
          command: data.command
        });
      }
    } catch (error) {
      console.error('Error processing message:', error);
    }
  });

  ws.on('close', () => {
    console.log('Client disconnected');
    activeClients.delete(ws);
  });
});

// Broadcast message to all connected WebSocket clients
function broadcastToClients(data) {
  const message = JSON.stringify(data);
  activeClients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(message);
    }
  });
}

// Query current state from router (including names)
function queryCurrentState() {
  // Get the maximum number of inputs and outputs to query from router configuration
  const maxInputs = routerConfig.inputs || 96;
  const maxOutputs = routerConfig.outputs || 384;

  console.log(`Starting router state query for ${maxInputs} inputs and ${maxOutputs} outputs`);

  // Broadcast initial status to clients
  broadcastToClients({
    type: 'nameQueryProgress',
    status: 'starting',
    totalInputs: maxInputs,
    totalOutputs: maxOutputs,
    message: `Starting to query names for ${maxInputs} inputs and ${maxOutputs} outputs...`
  });

  // Limit the number of queries to avoid overwhelming the router
  const maxQueries = 32; // Maximum number of queries to send at once

  if (routerConfig.protocol === 'rcp3') {
    // For RCP3 protocol

    // Query matrix status (which inputs are connected to which outputs)
    // Query in batches to avoid overwhelming the router
    for (let startDest = 1; startDest <= maxOutputs; startDest += maxQueries) {
      const numDests = Math.min(maxQueries, maxOutputs - startDest + 1);
      sendRouterCommand(JSON.stringify({
        command: 'getMatrix',
        startDest: startDest,
        numDests: numDests
      }));
    }

    // Query lock status in batches
    for (let startDest = 1; startDest <= maxOutputs; startDest += maxQueries) {
      const numDests = Math.min(maxQueries, maxOutputs - startDest + 1);
      sendRouterCommand(JSON.stringify({
        command: 'getLock',
        startDest: startDest,
        numDests: numDests
      }));
    }

    // Query ALL source names based on router configuration
    console.log(`Querying source names for inputs 1-${maxInputs}`);
    broadcastToClients({
      type: 'nameQueryProgress',
      status: 'querying_sources',
      current: 0,
      total: maxInputs,
      message: `Querying source names (0/${maxInputs})...`
    });

    querySourceNamesSequentially(1, maxInputs);

    // Query ALL destination names based on router configuration
    console.log(`Querying destination names for outputs 1-${maxOutputs}`);
    broadcastToClients({
      type: 'nameQueryProgress',
      status: 'querying_destinations',
      current: 0,
      total: maxOutputs,
      message: `Querying destination names (0/${maxOutputs})...`
    });

    queryDestinationNamesSequentially(1, maxOutputs);
  } else {
    // For Quartz protocol

    // Query which inputs are connected to outputs (sample only)
    const outputQueries = Math.min(maxOutputs, 64);
    for (let i = 1; i <= outputQueries; i++) {
      sendRouterCommand(`.IV${i}`);
    }

    // Query lock status (sample only)
    for (let i = 1; i <= outputQueries; i++) {
      sendRouterCommand(`.BI${i}`);
    }

    // Query ALL source names based on router configuration
    console.log(`Querying source names for inputs 1-${maxInputs}`);
    broadcastToClients({
      type: 'nameQueryProgress',
      status: 'querying_sources',
      current: 0,
      total: maxInputs,
      message: `Querying source names (0/${maxInputs})...`
    });

    querySourceNamesSequentially(1, maxInputs);

    // Query ALL destination names based on router configuration
    console.log(`Querying destination names for outputs 1-${maxOutputs}`);
    broadcastToClients({
      type: 'nameQueryProgress',
      status: 'querying_destinations',
      current: 0,
      total: maxOutputs,
      message: `Querying destination names (0/${maxOutputs})...`
    });

    queryDestinationNamesSequentially(1, maxOutputs);
  }
}

// Query current routing state from router (WITHOUT names)
function queryCurrentStateWithoutNames() {
  // Get the maximum number of inputs and outputs to query from router configuration
  const maxInputs = routerConfig.inputs || 96;
  const maxOutputs = routerConfig.outputs || 384;

  console.log(`Starting router routing state query for ${maxInputs} inputs and ${maxOutputs} outputs (names disabled)`);

  // Broadcast initial status to clients
  broadcastToClients({
    type: 'routingStateQuery',
    status: 'starting',
    totalInputs: maxInputs,
    totalOutputs: maxOutputs,
    message: `Querying routing state for ${maxInputs} inputs and ${maxOutputs} outputs...`
  });

  // Limit the number of queries to avoid overwhelming the router
  const maxQueries = 32; // Maximum number of queries to send at once

  if (routerConfig.protocol === 'rcp3') {
    // For RCP3 protocol

    // Query matrix status (which inputs are connected to which outputs)
    // Query in batches to avoid overwhelming the router
    for (let startDest = 1; startDest <= maxOutputs; startDest += maxQueries) {
      const numDests = Math.min(maxQueries, maxOutputs - startDest + 1);
      sendRouterCommand(JSON.stringify({
        command: 'getMatrix',
        startDest: startDest,
        numDests: numDests
      }));
    }

    // Query lock status in batches
    for (let startDest = 1; startDest <= maxOutputs; startDest += maxQueries) {
      const numDests = Math.min(maxQueries, maxOutputs - startDest + 1);
      sendRouterCommand(JSON.stringify({
        command: 'getLock',
        startDest: startDest,
        numDests: numDests
      }));
    }

    console.log('RCP3 routing state query completed (names skipped)');
  } else {
    // For Quartz protocol

    // Query which inputs are connected to outputs (sample only)
    const outputQueries = Math.min(maxOutputs, 64);
    for (let i = 1; i <= outputQueries; i++) {
      sendRouterCommand(`.IV${i}`);
    }

    // Query lock status (sample only)
    for (let i = 1; i <= outputQueries; i++) {
      sendRouterCommand(`.BI${i}`);
    }

    console.log('Quartz routing state query completed (names skipped)');
  }

  // Broadcast completion
  broadcastToClients({
    type: 'routingStateQuery',
    status: 'complete',
    message: 'Routing state query completed (names skipped for testing)'
  });
}

// Sequential source name querying with progress updates
function querySourceNamesSequentially(current, max) {
  if (current > max) {
    console.log(`Completed querying all ${max} source names`);
    broadcastToClients({
      type: 'nameQueryProgress',
      status: 'sources_complete',
      current: max,
      total: max,
      message: `Source names query completed (${max}/${max})`
    });
    return;
  }

  // Send query for current source
  if (routerConfig.protocol === 'rcp3') {
    sendRouterCommand(JSON.stringify({
      command: 'getSourceName',
      source: current
    }));
  } else {
    sendRouterCommand(`.RS${current}`);
  }

  // Update progress every 10 queries or at the end
  if (current % 10 === 0 || current === max) {
    console.log(`Querying source names: ${current}/${max}`);
    broadcastToClients({
      type: 'nameQueryProgress',
      status: 'querying_sources',
      current: current,
      total: max,
      message: `Querying source names (${current}/${max})...`
    });
  }

  // Schedule next query with delay to avoid overwhelming the router
  setTimeout(() => {
    querySourceNamesSequentially(current + 1, max);
  }, 50); // 50ms delay between queries (reduced from 100ms)
}

// Sequential destination name querying with progress updates
function queryDestinationNamesSequentially(current, max) {
  if (current > max) {
    console.log(`Completed querying all ${max} destination names`);
    broadcastToClients({
      type: 'nameQueryProgress',
      status: 'destinations_complete',
      current: max,
      total: max,
      message: `Destination names query completed (${max}/${max})`
    });

    // All queries completed
    broadcastToClients({
      type: 'nameQueryProgress',
      status: 'complete',
      message: 'All router names have been queried successfully'
    });
    return;
  }

  // Send query for current destination
  if (routerConfig.protocol === 'rcp3') {
    sendRouterCommand(JSON.stringify({
      command: 'getDestinationName',
      destination: current
    }));
  } else {
    sendRouterCommand(`.RD${current}`);
  }

  // Update progress every 10 queries or at the end
  if (current % 10 === 0 || current === max) {
    console.log(`Querying destination names: ${current}/${max}`);
    broadcastToClients({
      type: 'nameQueryProgress',
      status: 'querying_destinations',
      current: current,
      total: max,
      message: `Querying destination names (${current}/${max})...`
    });
  }

  // Schedule next query with delay to avoid overwhelming the router
  setTimeout(() => {
    queryDestinationNamesSequentially(current + 1, max);
  }, 50); // 50ms delay between queries (reduced from 100ms)
}

// Command queue and rate limiting
const commandQueue = [];
let isProcessingQueue = false;
const COMMAND_DELAY = 50; // ms between commands

// Send command to router
function sendRouterCommand(command) {
  if (routerConnected) {
    // Add command to queue and process
    commandQueue.push(command);
    if (!isProcessingQueue) {
      processCommandQueue();
    }
  } else {
    console.error('Cannot send command, not connected to router');
  }
}

// Process command queue with rate limiting
function processCommandQueue() {
  if (commandQueue.length === 0) {
    isProcessingQueue = false;
    return;
  }

  isProcessingQueue = true;
  const command = commandQueue.shift();
  console.log('Sending to router:', command);

  // Check if we're using the RCP3 protocol
  if (routerConfig.protocol === 'rcp3') {
    // For RCP3, we need to parse the JSON command and convert it to the proper binary format
    try {
      // Check if the command is a JSON string (RCP3 format)
      if (typeof command === 'string' && command.startsWith('{')) {
        const cmdObj = JSON.parse(command);
        const rcp3Command = formatRCP3Command(cmdObj);

        if (rcp3Command) {
          routerClient.write(rcp3Command);
          setTimeout(processCommandQueue, COMMAND_DELAY);
          return;
        }
      }
    } catch (error) {
      console.error('Error parsing RCP3 command:', error);
    }
  }

  // Default to Quartz protocol format
  routerClient.write(command + '\r');

  // Process next command after delay
  setTimeout(processCommandQueue, COMMAND_DELAY);
}

// Format RCP3 command
function formatRCP3Command(cmdObj) {
  // This is a simplified implementation of the RCP3 protocol
  // In a real implementation, this would create the proper binary packet with headers and checksums

  switch (cmdObj.command) {
    case 'take':
      // Create a Take command packet
      // Interface: 0x12, Command Type: 0x02
      console.log(`RCP3 Take: Destination ${cmdObj.destination}, Source ${cmdObj.source}, Levels ${cmdObj.levels}`);

      // TESTING: Simulate immediate response for testing
      setTimeout(() => {
        broadcastToClients({
          type: 'routerResponse',
          message: `.AV${cmdObj.destination},${cmdObj.source}`
        });
      }, 100);

      // In a real implementation, this would return a properly formatted binary packet
      // For now, we'll return a placeholder that the router would understand
      return Buffer.from(`RCP3_TAKE:${cmdObj.destination},${cmdObj.source},${cmdObj.levels}`);

    case 'setLock':
      // Create a Set Lock command packet
      // Interface: 0x12, Command Type: 0x0C
      console.log(`RCP3 Set Lock: Destination ${cmdObj.destination}, Lock Type ${cmdObj.lockType}, Levels ${cmdObj.levels}`);

      // In a real implementation, this would return a properly formatted binary packet
      return Buffer.from(`RCP3_LOCK:${cmdObj.destination},${cmdObj.lockType},${cmdObj.levels}`);

    case 'clearLock':
      // Create a Clear Lock command packet
      // Interface: 0x12, Command Type: 0x0E
      console.log(`RCP3 Clear Lock: Destination ${cmdObj.destination}, Levels ${cmdObj.levels}`);

      // In a real implementation, this would return a properly formatted binary packet
      return Buffer.from(`RCP3_UNLOCK:${cmdObj.destination},${cmdObj.levels}`);

    case 'getMatrix':
      // Create a Get Matrix command packet
      // Interface: 0x12, Command Type: 0x08
      console.log(`RCP3 Get Matrix: Start Dest ${cmdObj.startDest}, Num Dests ${cmdObj.numDests}`);

      // TESTING: Simulate response for testing - assume each output is connected to input with same number
      setTimeout(() => {
        for (let i = 0; i < cmdObj.numDests; i++) {
          const dest = cmdObj.startDest + i;
          const source = dest; // For testing, assume output N is connected to input N
          broadcastToClients({
            type: 'routerResponse',
            message: `.AV${dest},${source}`
          });
        }
      }, 50);

      // In a real implementation, this would return a properly formatted binary packet
      return Buffer.from(`RCP3_GET_MATRIX:${cmdObj.startDest},${cmdObj.numDests}`);

    case 'getLock':
      // Create a Get Lock command packet
      // Interface: 0x12, Command Type: 0x0D
      console.log(`RCP3 Get Lock: Start Dest ${cmdObj.startDest}, Num Dests ${cmdObj.numDests}`);

      // In a real implementation, this would return a properly formatted binary packet
      return Buffer.from(`RCP3_GET_LOCK:${cmdObj.startDest},${cmdObj.numDests}`);

    case 'getSourceName':
      // This is not a standard RCP3 command, but we'll include it for completeness
      console.log(`RCP3 Get Source Name: Source ${cmdObj.source}`);

      // In a real implementation, this would use a different mechanism to get source names
      return Buffer.from(`RCP3_GET_SOURCE_NAME:${cmdObj.source}`);

    case 'getDestinationName':
      // This is not a standard RCP3 command, but we'll include it for completeness
      console.log(`RCP3 Get Destination Name: Destination ${cmdObj.destination}`);

      // In a real implementation, this would use a different mechanism to get destination names
      return Buffer.from(`RCP3_GET_DEST_NAME:${cmdObj.destination}`);

    case 'setSourceName':
      // Set source name command
      console.log(`RCP3 Set Source Name: Source ${cmdObj.source}, Name "${cmdObj.name}"`);

      // Store the name in our local cache
      routerNames.sources[cmdObj.source] = cmdObj.name;

      // In a real implementation, this would return a properly formatted binary packet
      return Buffer.from(`RCP3_SET_SOURCE_NAME:${cmdObj.source},${cmdObj.name}`);

    case 'setDestinationName':
      // Set destination name command
      console.log(`RCP3 Set Destination Name: Destination ${cmdObj.destination}, Name "${cmdObj.name}"`);

      // Store the name in our local cache
      routerNames.destinations[cmdObj.destination] = cmdObj.name;

      // In a real implementation, this would return a properly formatted binary packet
      return Buffer.from(`RCP3_SET_DEST_NAME:${cmdObj.destination},${cmdObj.name}`);

    default:
      console.error(`Unknown RCP3 command: ${cmdObj.command}`);
      return null;
  }
}

// Try multiple ports if the default one is in use
function startServer(port) {
  server.listen(port)
    .on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        console.log(`Port ${port} is in use, trying port ${port + 1}`);
        startServer(port + 1);
      } else {
        console.error('Server error:', error);
      }
    })
    .on('listening', () => {
      const actualPort = server.address().port;
      console.log(`Server running on http://localhost:${actualPort}`);
      // Don't connect to router automatically on startup
      // Connection will be initiated by user clicking Connect button
    });
}

// Start server on default port, with fallback to other ports if needed
startServer(defaultPort);